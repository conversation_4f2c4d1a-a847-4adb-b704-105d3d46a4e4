# 百度网盘大文件上传功能说明

## 概述

基于对百度网盘移动端API的分析，我们优化了 `BaiduUpload.py` 脚本，现在支持大文件（20GB+）的分片上传功能。

## 新增功能

### 1. 自动文件大小检测
- 文件 ≤ 100MB：使用原有的单文件上传模式
- 文件 > 100MB：自动切换到分片上传模式

### 2. 分片上传机制
- **分片大小**：4MB per chunk
- **并发控制**：顺序上传，避免频率限制
- **断点续传**：支持 `x-enable-resume=1` 参数
- **校验机制**：每个分片都有独立的MD5校验

### 3. 移动端API兼容
- 支持移动端API参数格式
- 正确的multipart/form-data格式
- 设备指纹和时间戳验证

## 核心改进（按正确抓包流程）

### 1. 正确的API流程
```
1. precreate - 预创建，传递客户端计算的所有分片MD5
2. getpartlist - 获取服务器要求上传的分片信息（包括实际大小）
3. superfile2 - 按服务器要求的分片信息上传
4. create - 使用客户端完整MD5列表创建文件
```

### 2. 分片MD5预计算
```python
def calculate_file_block_list(self, file_path, chunk_size=4*1024*1024):
    """计算文件的分片MD5列表（用于precreate）"""
    # 按4MB分片计算所有MD5，用于precreate API
```

### 3. 服务器分片信息获取
```python
def get_part_list(self, uploadid, file_path, part_ids):
    """获取服务器要求的分片信息"""
    # 关键：服务器决定实际分片大小，不是固定4MB
    # 返回：part_id, part_size, part_md5
```

### 4. 按服务器要求上传
```python
def upload_chunk_by_server_info(self, file_path, part_info, upload_info):
    """根据服务器返回的分片信息上传分片"""
    # 关键参数：
    # - partseq: 服务器指定的分片ID
    # - partoffset: 根据分片ID计算的偏移量
    # - x-part-offset: 服务器指定的分片大小
    # - x-part-etag: 服务器指定的分片MD5
```

## 使用方法

### 1. 基本使用
```python
from BaiduUpload import BaiduUpload

uploader = BaiduUpload()
uploader.login_with_cookie("你的Cookie")

# 自动检测文件大小并选择合适的上传方式
success = uploader.upload_file("large_file.zip", "/")
```

### 2. 强制使用大文件上传
```python
# 专门用于大文件的方法
success = uploader.upload_large_file("very_large_file.zip", "/")
```

### 3. 测试脚本
```bash
# 运行测试脚本
python test_large_upload.py
```

## 配置要求

### 1. Cookie配置
创建 `cookies.json` 文件：
```json
{
  "baidu": "你的百度网盘Cookie字符串"
}
```

### 2. 获取Cookie方法
1. 登录百度网盘网页版
2. 打开浏览器开发者工具 (F12)
3. 在Network标签页找到任意请求
4. 复制Cookie字段的完整值

## 性能特点

### 1. 上传速度
- **分片并发**：虽然是顺序上传，但减少了单次请求的数据量
- **网络优化**：4MB分片大小平衡了速度和稳定性
- **错误恢复**：单个分片失败不影响整体进度

### 2. 内存使用
- **流式处理**：不会将整个文件加载到内存
- **分片缓存**：每次只处理4MB数据
- **及时释放**：上传完成后立即释放分片数据

### 3. 稳定性
- **重试机制**：分片上传失败可以单独重试
- **校验保证**：每个分片都有MD5校验
- **进度显示**：实时显示上传进度

## 技术细节

### 1. 正确的API流程（基于抓包分析）
```
1. 前置验证 (移动端特有)
   ├── 设备验证 (/wfm/ndsr)
   ├── 权限检查 (/api/singkil/bindquery)
   └── 反作弊检测 (/afd/entry)

2. 文件预创建
   ├── 计算所有分片MD5 (calculate_file_block_list)
   ├── 预创建任务 (/api/precreate) - 传递完整MD5列表
   └── 获取服务器返回的block_list

3. 获取分片信息
   ├── 调用getpartlist (/api/batch/getpartlist)
   ├── 传递服务器要求的分片ID列表
   └── 获取每个分片的实际大小和MD5

4. 分片上传
   ├── 按服务器要求读取分片数据
   ├── 上传每个分片 (/rest/2.0/pcs/superfile2)
   └── 使用服务器指定的分片参数

5. 文件创建
   ├── 使用客户端完整MD5列表 (/api/create)
   ├── 获取配额信息 (/api/quota)
   └── 刷新文件列表 (/api/list)
```

### 2. 关键参数对比

| 参数 | 网页端 | 移动端 | 说明 |
|------|--------|--------|------|
| clienttype | 0 | 1 | 客户端类型 |
| partseq | 0 | 实际序号 | 分片序号 |
| partoffset | - | 实际偏移 | 分片偏移量 |
| x-part-offset | - | 分片大小 | 分片大小 |
| x-part-etag | - | 分片MD5 | 分片校验 |
| x-enable-resume | - | 1 | 断点续传 |

## 故障排除

### 1. 常见错误
- **Cookie过期**：重新获取Cookie
- **网络超时**：检查网络连接，减小分片大小
- **权限不足**：确认VIP状态和存储空间

### 2. 调试方法
- 启用详细日志输出
- 检查每个分片的上传状态
- 验证分片MD5值

### 3. 性能调优
- 调整分片大小 (默认4MB)
- 修改上传间隔 (默认0.1秒)
- 优化网络连接

## 注意事项

1. **Cookie安全**：不要泄露Cookie信息
2. **频率限制**：避免过于频繁的请求
3. **存储空间**：确保网盘有足够空间
4. **网络稳定**：大文件上传需要稳定的网络环境
5. **VIP权限**：大文件上传可能需要VIP权限

## 更新日志

- **v2.0**: 添加大文件分片上传支持
- **v1.0**: 基础单文件上传功能
