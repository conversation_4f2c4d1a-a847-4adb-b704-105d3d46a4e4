#!/usr/bin/env python3
import gzip
import json

# 读取响应体
with open('20250730_160209/20250730_160209/0/response_body', 'rb') as f:
    data = f.read()

print(f"原始数据长度: {len(data)}")

# 解析HTTP chunked encoding
lines = data.split(b'\r\n')
print(f"分割后行数: {len(lines)}")

# 第一行应该是chunk大小（十六进制）
chunk_size_hex = lines[0].decode('ascii')
chunk_size = int(chunk_size_hex, 16)
print(f"Chunk大小: {chunk_size_hex} = {chunk_size} 字节")

# 第二行是实际的gzip数据
gzip_data = lines[1]
print(f"Gzip数据长度: {len(gzip_data)}")

try:
    # 解压gzip数据
    decompressed = gzip.decompress(gzip_data)
    print(f"解压后长度: {len(decompressed)}")
    
    # 解析JSON
    result = json.loads(decompressed.decode('utf-8'))
    print("成功解析JSON!")
    
    # 显示关键信息
    print(f"errno: {result.get('errno')}")
    print(f"return_type: {result.get('return_type')}")
    
    block_list = result.get('block_list', [])
    print(f"block_list长度: {len(block_list)}")
    if block_list:
        print(f"前10个block_list: {block_list[:10]}")
        print(f"后10个block_list: {block_list[-10:]}")
    
    uploadid = result.get('uploadid')
    print(f"uploadid: {uploadid}")
    
    # 保存完整结果到文件
    with open('precreate_response.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    print("完整响应已保存到 precreate_response.json")
    
except Exception as e:
    print(f"解析失败: {e}")
    print(f"Gzip数据前20字节: {gzip_data[:20].hex()}")
