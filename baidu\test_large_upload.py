#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件上传测试脚本
"""

import os
import sys
import json
from BaiduUpload import BaiduUpload

def create_test_file(size_mb=200):
    """创建指定大小的测试文件"""
    filename = f"test_large_file_{size_mb}MB.bin"
    print(f"🔄 创建 {size_mb}MB 测试文件: {filename}")
    
    chunk_size = 1024 * 1024  # 1MB chunks
    with open(filename, 'wb') as f:
        for i in range(size_mb):
            # 写入1MB的数据
            data = bytes([i % 256] * chunk_size)
            f.write(data)
            if (i + 1) % 50 == 0:
                print(f"   已写入: {i + 1}/{size_mb} MB")
    
    print(f"✅ 测试文件创建完成: {filename}")
    return filename

def test_large_file_upload():
    """测试大文件上传"""
    uploader = BaiduUpload()
    
    # 读取Cookie
    try:
        with open('cookies.json', 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            baidu_cookie = cookies_data.get('baidu', '')
            
        if not baidu_cookie:
            print("❌ 未找到百度网盘Cookie，请先配置cookies.json")
            return False
            
        uploader.login_with_cookie(baidu_cookie)
        
    except Exception as e:
        print(f"❌ Cookie配置错误: {str(e)}")
        return False
    
    # 询问测试方式
    print("\n请选择测试方式:")
    print("1. 创建200MB测试文件并上传")
    print("2. 创建1GB测试文件并上传")
    print("3. 上传现有文件")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    test_file = None
    cleanup_file = False
    
    try:
        if choice == "1":
            test_file = create_test_file(200)
            cleanup_file = True
        elif choice == "2":
            test_file = create_test_file(1024)
            cleanup_file = True
        elif choice == "3":
            test_file = input("请输入文件路径: ").strip().strip('"')
            if not os.path.exists(test_file):
                print(f"❌ 文件不存在: {test_file}")
                return False
        else:
            print("❌ 无效选择")
            return False
        
        # 显示文件信息
        file_size = os.path.getsize(test_file)
        print(f"\n📁 文件信息:")
        print(f"   - 文件名: {os.path.basename(test_file)}")
        print(f"   - 文件大小: {file_size/(1024**3):.2f} GB")
        print(f"   - 预计分片数: {(file_size + 4*1024*1024 - 1) // (4*1024*1024)}")
        
        # 确认上传
        confirm = input(f"\n确认上传文件 '{os.path.basename(test_file)}' 吗? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消上传")
            return False
        
        # 执行上传
        print(f"\n🚀 开始上传...")
        success = uploader.upload_file(test_file, "/test/")
        
        if success:
            print(f"\n🎉 大文件上传测试成功!")
            return True
        else:
            print(f"\n❌ 大文件上传测试失败!")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        return False
        
    finally:
        # 清理测试文件
        if cleanup_file and test_file and os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"🗑️  已清理测试文件: {test_file}")
            except Exception as e:
                print(f"⚠️  清理测试文件失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 百度网盘大文件上传测试")
    print("=" * 60)
    
    # 检查cookies.json文件
    if not os.path.exists('cookies.json'):
        print("❌ 未找到cookies.json文件")
        print("请创建cookies.json文件，格式如下:")
        print('{')
        print('  "baidu": "你的百度网盘Cookie"')
        print('}')
        return
    
    # 执行测试
    success = test_large_file_upload()
    
    print("=" * 60)
    if success:
        print("✅ 测试完成 - 成功")
    else:
        print("❌ 测试完成 - 失败")
    print("=" * 60)

if __name__ == "__main__":
    main()
