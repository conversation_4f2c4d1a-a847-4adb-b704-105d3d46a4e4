<template>
  <div class="cloud-transfer">
    <!-- 消息提示 -->
    <div v-if="message" class="message-container">
      <div :class="['message', `message-${messageType}`]">
        {{ message }}
      </div>
    </div>

    <!-- 登录提示弹窗 -->
    <LoginPrompt
      v-if="showLoginPrompt"
      title="网盘间传输功能需要登录"
      message="您需要先登录网盘账号并配置Cookie信息才能使用网盘间传输功能。"
      @close="showLoginPrompt = false"
    />

    <!-- 左右两栏布局 -->
    <div class="transfer-layout">
      <!-- 左侧设置面板 -->
      <div class="left-panel">
        <h3 class="panel-title">网盘间传输设置</h3>

        <!-- 传输方向选择 -->
        <div class="direction-section">
          <label class="section-label">
            传输方向 <span class="required">*</span>
          </label>
          <div class="direction-selector">
            <div 
              class="direction-option"
              :class="{ active: transferDirection === 'quark-to-baidu' }"
              @click="setTransferDirection('quark-to-baidu')"
            >
              <div class="direction-flow">
                <div class="source-disk">
                  <img src="/icons/quark.svg" alt="夸克网盘" class="disk-icon">
                  <span>夸克网盘</span>
                </div>
                <div class="arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
                <div class="target-disk">
                  <img src="/icons/baidu.svg" alt="百度网盘" class="disk-icon">
                  <span>百度网盘</span>
                </div>
              </div>
            </div>
            <div 
              class="direction-option"
              :class="{ active: transferDirection === 'baidu-to-quark' }"
              @click="setTransferDirection('baidu-to-quark')"
            >
              <div class="direction-flow">
                <div class="source-disk">
                  <img src="/icons/baidu.svg" alt="百度网盘" class="disk-icon">
                  <span>百度网盘</span>
                </div>
                <div class="arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
                <div class="target-disk">
                  <img src="/icons/quark.svg" alt="夸克网盘" class="disk-icon">
                  <span>夸克网盘</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 保存到哪里 -->
        <div class="target-section">
          <label class="section-label">
            保存到哪里 <span class="required">*</span>
          </label>
          <div class="folder-input">
            <i class="fas fa-folder"></i>
            <input
              type="text"
              v-model="targetFolderPath"
              placeholder="请输入保存路径，如：/我的文件/传输文件夹"
              class="path-input"
            >
          </div>
          <div class="folder-desc">输入文件要保存到目标网盘的哪个文件夹</div>
        </div>

        <!-- 要传输什么 -->
        <div class="source-section">
          <label class="section-label">
            要传输什么 <span class="required">*</span>
          </label>
          <div class="file-input">
            <i class="fas fa-file"></i>
            <input
              type="text"
              v-model="sourceFilePath"
              placeholder="点击选择要传输的文件或文件夹"
              readonly
              class="path-input path-input-full"
            >
            <button
              @click="selectSourceFiles"
              class="select-btn"
              :disabled="loading"
            >
              <i class="fas fa-folder-open"></i>
              选择文件
            </button>
          </div>
          <div class="file-desc">选择要传输的文件或文件夹（支持多选）</div>

          <!-- 已选择文件预览区域 - 固定高度 -->
          <div class="selected-files-container">
            <div v-if="selectedFiles.length > 0" class="selected-files">
              <div class="selected-files-header">
                <span class="files-count">已选择 {{ selectedFiles.length }} 个项目</span>
                <button @click="clearSelectedFiles" class="clear-all-btn">
                  <i class="fas fa-trash"></i>
                  清空
                </button>
              </div>
              <div class="selected-files-list">
                <div
                  v-for="(file, index) in selectedFiles"
                  :key="file.fid"
                  class="selected-file-item"
                  :class="{ 'is-folder': file.is_folder }"
                >
                  <!-- 文件预览卡片 -->
                  <div class="file-card" @click="toggleFilePreview(index)">
                    <div class="file-card-inner" :class="{ flipped: file.showPreview }">
                      <!-- 正面 -->
                      <div class="file-card-front">
                        <div class="file-info">
                          <i :class="getFileIcon(file)" class="file-icon"></i>
                          <div class="file-details">
                            <div class="file-name">{{ file.file_name }}</div>
                            <div class="file-meta">
                              <span class="file-size">{{ formatFileSize(file.size) }}</span>
                              <span class="file-type">{{ file.is_folder ? '文件夹' : '文件' }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- 背面 -->
                      <div class="file-card-back">
                        <div class="file-preview-info">
                          <div class="preview-item">
                            <span class="label">文件ID:</span>
                            <span class="value">{{ file.fid }}</span>
                          </div>
                          <div class="preview-item">
                            <span class="label">类型:</span>
                            <span class="value">{{ file.is_folder ? '文件夹' : getFileType(file.file_name) }}</span>
                          </div>
                          <div class="preview-item">
                            <span class="label">大小:</span>
                            <span class="value">{{ formatFileSize(file.size) }}</span>
                          </div>
                          <div class="preview-item" v-if="file.is_folder">
                            <span class="label">预计传输:</span>
                            <span class="value">整个文件夹</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button @click="removeSelectedFile(index)" class="remove-btn">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="empty-selection">
              <div class="empty-text">
                <i class="fas fa-file-plus"></i>
                <span>点击上方按钮选择要传输的内容</span>
              </div>
            </div>
          </div>
        </div>



        <!-- 执行按钮 -->
        <div class="action-section">
          <button
            @click="handleExecuteClick"
            class="execute-btn"
            :class="{ 
              'btn-primary': !loading,
              'btn-success': loading && isPaused,
              'btn-disabled': loading && !isPaused
            }"
            :disabled="!canExecute || (loading && !isPaused)"
          >
            <i :class="getExecuteButtonIcon"></i>
            {{ getExecuteButtonText }}
          </button>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 翻转容器 -->
        <div class="flip-container" :class="{ flipped: showFileBrowser }">
          <!-- 正面：日志面板 -->
          <div class="flip-front">
            <div class="results-panel">
              <h3 class="panel-title">
                传输日志
                <span class="title-controls">
                  <i
                    v-if="loading"
                    @click="togglePause"
                    :class="isPaused ? 'fas fa-play' : 'fas fa-pause'"
                    class="control-icon pause-icon"
                    :title="isPaused ? '继续传输' : '暂停传输'"
                  ></i>
                  <i
                    v-if="loading"
                    @click="stopTask"
                    class="fas fa-stop control-icon stop-icon"
                    title="停止传输"
                  ></i>
                  <i
                    v-if="logs.length > 0"
                    @click="clearLogs"
                    class="fas fa-trash control-icon clear-icon"
                    title="清理日志"
                  ></i>
                </span>
              </h3>

              <!-- 传输进度 -->
              <div v-if="loading" class="progress-section">
                <div class="progress-info">
                  <span class="progress-text">{{ progressText }}</span>
                  <span class="progress-percentage">{{ Math.round(progress) }}%</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: progress + '%' }"></div>
                </div>
              </div>

              <!-- 日志容器 -->
              <div class="log-container">
                <div class="empty-logs" v-if="logs.length === 0">
                  <div class="empty-text">尚未开始传输，结果将在这里显示</div>
                </div>
                <div class="log-list" v-else>
                  <div
                    v-for="(log, index) in logs"
                    :key="index"
                    class="log-item"
                    :class="[
                      log.type,
                      {
                        separator: log.isSeparator,
                        'task-start': log.isTaskStart,
                        'task-end': log.isTaskEnd,
                        'sub-task': log.isSubTask
                      }
                    ]"
                    :style="{ paddingLeft: `${0.5 + (log.indent || 0) * 1.5}rem` }"
                  >
                    <span v-if="log.time && !log.isSeparator" class="log-time">{{ log.time }}</span>
                    <span class="log-message" :class="{ 'no-time': !log.time || log.isSeparator }">{{ log.message }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 背面：文件浏览器 -->
          <div class="flip-back">
            <div class="browser-panel">
              <div class="browser-header">
                <h3 class="panel-title">文件浏览器</h3>
                <button @click="closeFileBrowser" class="close-browser-btn">
                  <i class="fas fa-times"></i>
                  关闭
                </button>
              </div>
              <div class="browser-container">
                <FileBrowser
                  ref="fileBrowser"
                  :compact-mode="true"
                  @file-selected="handleFileSelected"
                  @folder-selected="handleFolderSelected"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useDiskSwitch } from '../composables/useDiskSwitch'
import { usePermissionCheck } from '../composables/usePermissionCheck'
import LoginPrompt from './LoginPrompt.vue'
import FileBrowser from './FileBrowser.vue'
import { QuarkDownloadService } from '../services/quarkDownloadService'
import { BaiduUploadService } from '../services/baiduUploadService'

// 基础状态
const authStore = useAuthStore()
const { currentDiskType } = useDiskSwitch({
  componentName: 'CloudTransfer'
})
const { getUserCookie } = usePermissionCheck()

// UI状态
const showLoginPrompt = ref(false)
const showFileBrowser = ref(false)
const loading = ref(false)
const isPaused = ref(false)
const pauseController = ref<AbortController | null>(null)
const currentSelectionMode = ref<'source' | 'target'>('source') // 当前选择模式

// 传输配置
const transferDirection = ref<'quark-to-baidu' | 'baidu-to-quark'>('quark-to-baidu')
const sourceFilePath = ref('')
const targetFolderPath = ref('')
const selectedFiles = ref<any[]>([])

// Cookie配置 - 夸克从设置获取，百度使用硬编码
const cookieConfig = computed(() => {
  const quarkCookie = getUserCookie() || ''

  // 调试：显示获取到的Cookie信息
  console.log('🔍 [CloudTransfer] 从设置获取的夸克Cookie长度:', quarkCookie.length)
  console.log('🔍 [CloudTransfer] 从设置获取的夸克Cookie前100字符:', quarkCookie.substring(0, 100))

  // 百度网盘Cookie保持硬编码
  const baiduCookie = 'XFI=35229335-ad2c-ae53-4b98-13a75cbc7374; XFCS=42A03FF5771E92D3DD98680615EBE9421F1C4F8F40D22028CA6637E3C4621929; XFT=jIXBuWOZ1T21vUHHXCUl9d6V0epfBtcJ1A/lK8nqRk8=; csrfToken=W23rb3vs5_LgPiRMAlnhQ87g; PSTM=1738467890; BIDUPSID=5F2AB08908E9B3D603DCB1AAFD638CE4; BDSFRCVID=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; HMACCOUNT=E06BDF8E3EA1166E; PANWEB=1; Hm_lpvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; Hm_lvt_95fc87a381fad8fcb37d76ac51fefcea=**********,**********; Hm_lpvt_95fc87a381fad8fcb37d76ac51fefcea=**********; XFI=f82a60a0-1881-11f0-96de-ddea43cfd8b4; XFCS=DACD04E678A6B6FBAAC9EB2A0447F305B9C6026C72BA17195959F355364A2541; XFT=UyFoh0F56M4AIeSuLoPWo5QWRKf13iDeR1RwIG1YnTg=; BDUSS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; BDUSS_BFESS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; H_WISE_SIDS_BFESS=110085_626068_628198_632156_633618_632291_632300_641765_642956_643573_641767_644369_644665_644645_645089_645170_645697_645921_644402_646540_646556_646774_646740_645030_647625_647664_647709_647691_647902_647927_648250_648403_648453_648452_648467_648464_648471_648479_648450_648500_648474_648498_648477_648505_648502_648506_648473_648461_648432_648442_648590_648725_648743_649009_649056_649073_649065_649050_649018_648986_648587_648094_649186_649234_646542_649356_649344_641262_649532_649589_649659_649650_649760_649713_649751_649775_649809_649869_649871_649778_649910_649230_649928_649959_649936_649920_650034_650069_650049_650041_639680_649885_644053_650204_650215_650210_650218_650084_650270_650259_650262_650288_650285_650312_650329_650324_650323_650328_650420; MCITY=-119%3A; Hm_lvt_fa0277816200010a74ab7d2895df481b=1750308228; Hm_lpvt_fa0277816200010a74ab7d2895df481b=1750657333; Hm_lvt_d5bdc9eee733100f7b952fc44f7e53e4=1749284833,1751556345; Hm_lpvt_d5bdc9eee733100f7b952fc44f7e53e4=1751598458; Hm_lvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1750215916,1752144625; Hm_lpvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1752144625; BAIDUID=B6C9C93D66164CBA01B02AED75FF179B:FG=1; newlogin=1; BAIDUID_BFESS=B6C9C93D66164CBA01B02AED75FF179B:FG=1; BDSFRCVID_BFESS=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF_BFESS=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; delPer=0; PSINO=7; ZFY=3nckonzJ6aE80MKyUr4y8zQoAPXHmiqyK35M1BfC5XY:C; BDCLND=tE3Nof7QnlSVboq1mb1gUQnAYUJV0sXIZ7bpW714pVE%3D; STOKEN=424372d096960b2d01ce2b9e63e4e05ac1302157657cc94cacdb6c903accb967; H_PS_PSSID=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; H_WISE_SIDS=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; Hm_lvt_182d6d59474cf78db37e0b2248640ea5=1753414973; Hm_lpvt_182d6d59474cf78db37e0b2248640ea5=1753553612; ndut_fmt=C43E2B2038A6168778CCFA43292A4BD592ADBFBC98D2B62722F4AA2992B3EE66; ab_sr=1.0.1_NTI1ZDhhZGExN2U2ZjNjZGUyZDUwNjc3Yzg2YzQ0MjFkMGUxY2Q1MzFlYmJkYWFhN2E0ZjIzY2M1YmQwMDBiYzllNTZmNTI3MTRkNzU5NjYxYzY3NDAzY2UyMjRlNDJlZWM1YTBiNjIxZjgwNWJhNzE4MDBjNDRlYzc0MTY1NTQwM2M4MDhhY2VlN2E2NzEwZjNiNGFmZmViMzliMTVkMjg5MTZkMmRiZTA4NzJmZmU5NTNhZmE5YTViZTM2YWUy; PANPSC=7818778965945482978%3AnHZOtuqy9avxzjnZjz4PEFcS2d9ns3O5C61tf8CKQkjghmkhEDEibA3MPJHnDhkCz81ttRoL0tCAzpMjKfHvSlRJmYG59WfnDmIVxiIPZ%2BhtcQe%2BbgwEaTCyYQOwAMBA4CvFohGLzm4xCCiUPpEZ9rj2cUzyGc10baC532AuNi3w0iRW66wpccQp2WmIaeM4j9JYwYAV4WIE%2BW3bn8WQpiM3H8XB9kjAw%2FFO59HDGjBoPH46caSWouLaMusRzMQHj%2FrJb%2BRIEq3GB1Ggo%2FVEtCYpmymoXX%2BPeeyDNZbt4BHtb5iP4kyX6ZKxVlqrIDxM'

  return {
    quark: quarkCookie,
    baidu: baiduCookie
  }
})

// 传输选项 - 设为默认行为，不再让用户选择
const transferOptions = {
  overwrite: false,        // 不覆盖同名文件（安全起见）
  keepStructure: true,     // 保持文件夹结构
  skipErrors: true         // 跳过错误继续传输
}

// 进度和日志
const progress = ref(0)
const progressText = ref('')
const logs = ref<any[]>([])

// 消息相关
const message = ref('')
const messageType = ref<'success' | 'error' | 'warning' | 'info'>('info')

// 显示消息
const showMessage = (msg: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  message.value = msg
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

// 计算属性
const canExecute = computed(() => {
  return selectedFiles.value.length > 0 && targetFolderPath.value && !loading.value
})

const getExecuteButtonIcon = computed(() => {
  if (loading.value && isPaused.value) return 'fas fa-play'
  if (loading.value) return 'fas fa-spinner fa-spin'
  return 'fas fa-exchange-alt'
})

const getExecuteButtonText = computed(() => {
  if (loading.value && isPaused.value) return '继续传输'
  if (loading.value) return '传输中...'
  return '开始传输'
})

// 方法占位符（后续实现）
const setTransferDirection = (direction: 'quark-to-baidu' | 'baidu-to-quark') => {
  transferDirection.value = direction
  // 清空已选择的文件，因为切换了源网盘
  selectedFiles.value = []
  sourceFilePath.value = ''
  targetFolderPath.value = ''
}

const selectSourceFiles = () => {
  currentSelectionMode.value = 'source'
  showMessage('请在文件浏览器中选择要传输的文件（可多选）', 'info')
  showFileBrowser.value = true
}



const clearSelectedFiles = () => {
  selectedFiles.value = []
  sourceFilePath.value = ''
}

const removeSelectedFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  if (selectedFiles.value.length === 0) {
    sourceFilePath.value = ''
  }
}

const toggleFilePreview = (index: number) => {
  selectedFiles.value[index].showPreview = !selectedFiles.value[index].showPreview
}

const handleExecuteClick = () => {
  if (loading.value && isPaused.value) {
    resumeTask()
  } else if (!loading.value) {
    startTransfer()
  }
}

const startTransfer = async () => {
  if (!selectedFiles.value.length || !targetFolderPath.value) {
    showMessage('请选择要传输的文件和目标路径', 'warning')
    return
  }

  // 检查是否已登录和配置
  if (!authStore.isLoggedIn || !authStore.currentDiskCookie) {
    showMessage('请先登录并配置网盘Cookie', 'warning')
    showLoginPrompt.value = true
    return
  }

  loading.value = true
  progress.value = 0
  progressText.value = '准备开始传输...'

  // 执行传输时自动翻转到日志区
  if (showFileBrowser.value) {
    showFileBrowser.value = false
  }

  try {
    addLog('开始网盘间传输任务', 'info', { isTaskStart: true, showTime: true })
    addLog(`传输方向: ${transferDirection.value === 'quark-to-baidu' ? '夸克网盘 → 百度网盘' : '百度网盘 → 夸克网盘'}`, 'info')
    addLog(`文件数量: ${selectedFiles.value.length} 个`, 'info')
    addLog(`保存路径: ${targetFolderPath.value}`, 'info')
    addLog(`传输选项: 不覆盖同名文件, 保持文件夹结构, 跳过错误继续传输`, 'info')

    // 检查Cookie配置
    if (!cookieConfig.value.quark || !cookieConfig.value.baidu) {
      showMessage('请先配置夸克网盘和百度网盘的Cookie', 'warning')
      return
    }

    // 验证百度网盘Cookie格式
    const baiduCookieValid = cookieConfig.value.baidu.includes('BDUSS') && cookieConfig.value.baidu.includes('STOKEN')
    if (!baiduCookieValid) {
      showMessage('百度网盘Cookie格式不正确，请确保包含BDUSS和STOKEN字段', 'warning')
      addLog('百度网盘Cookie必须包含BDUSS和STOKEN字段才能正常工作', 'error')
      return
    }

    // 直接使用下载测试的逻辑 - 创建QuarkDownloadService
    const quarkCookie = getUserCookie()
    if (!quarkCookie) {
      showMessage('请先在设置中配置夸克网盘Cookie', 'warning')
      return
    }

    addLog(`使用设置中的夸克网盘Cookie进行下载`, 'info')
    addLog(`夸克Cookie长度: ${quarkCookie.length}`, 'info')

    // 直接创建下载测试中使用的服务实例
    const quarkService = new QuarkDownloadService(quarkCookie)

    // 硬编码百度网盘Cookie用于上传
    const baiduCookie = 'XFI=35229335-ad2c-ae53-4b98-13a75cbc7374; XFCS=42A03FF5771E92D3DD98680615EBE9421F1C4F8F40D22028CA6637E3C4621929; XFT=jIXBuWOZ1T21vUHHXCUl9d6V0epfBtcJ1A/lK8nqRk8=; csrfToken=W23rb3vs5_LgPiRMAlnhQ87g; PSTM=1738467890; BIDUPSID=5F2AB08908E9B3D603DCB1AAFD638CE4; BDSFRCVID=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; HMACCOUNT=E06BDF8E3EA1166E; PANWEB=1; Hm_lpvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; Hm_lvt_95fc87a381fad8fcb37d76ac51fefcea=**********,**********; Hm_lpvt_95fc87a381fad8fcb37d76ac51fefcea=**********; XFI=f82a60a0-1881-11f0-96de-ddea43cfd8b4; XFCS=DACD04E678A6B6FBAAC9EB2A0447F305B9C6026C72BA17195959F355364A2541; XFT=UyFoh0F56M4AIeSuLoPWo5QWRKf13iDeR1RwIG1YnTg=; BDUSS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; BDUSS_BFESS=gxek1kTWxqYVU3M3ljYnRiMFU5V0xtM29xUGRYWU44ZnJ-ZkdoMjN4YTdieU5vSVFBQUFBJCQAAAAAAAAAAAEAAAASUZrn0MfSsLXEt9bP7QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALvi-2e74vtncl; H_WISE_SIDS_BFESS=110085_626068_628198_632156_633618_632291_632300_641765_642956_643573_641767_644369_644665_644645_645089_645170_645697_645921_644402_646540_646556_646774_646740_645030_647625_647664_647709_647691_647902_647927_648250_648403_648453_648452_648467_648464_648471_648479_648450_648500_648474_648498_648477_648505_648502_648506_648473_648461_648432_648442_648590_648725_648743_649009_649056_649073_649065_649050_649018_648986_648587_648094_649186_649234_646542_649356_649344_641262_649532_649589_649659_649650_649760_649713_649751_649775_649809_649869_649871_649778_649910_649230_649928_649959_649936_649920_650034_650069_650049_650041_639680_649885_644053_650204_650215_650210_650218_650084_650270_650259_650262_650288_650285_650312_650329_650324_650323_650328_650420; MCITY=-119%3A; Hm_lvt_fa0277816200010a74ab7d2895df481b=1750308228; Hm_lpvt_fa0277816200010a74ab7d2895df481b=1750657333; Hm_lvt_d5bdc9eee733100f7b952fc44f7e53e4=1749284833,1751556345; Hm_lpvt_d5bdc9eee733100f7b952fc44f7e53e4=1751598458; Hm_lvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1750215916,1752144625; Hm_lpvt_0ba7bcf57b5e55fbfbab9a2750acdf3e=1752144625; BAIDUID=B6C9C93D66164CBA01B02AED75FF179B:FG=1; newlogin=1; BAIDUID_BFESS=B6C9C93D66164CBA01B02AED75FF179B:FG=1; BDSFRCVID_BFESS=pAIOJexroGWUFd6JC6OBbo5DjeKK0gOTDYrEOwXPsp3LGJLVdnYyEG0PtDQ0HUCM4ch-ogKK3gOTHxDF_2uxOjjg8UtVJeC6EG0Ptf8g0M5; H_BDCLCKID_SF_BFESS=JR4H_CIhfCK3fP36q4Oo5tD_hgT22-us5N4J2hcHMPoosU3kLJA5yxJbWRJa25QR2DTiaKJjBMbUotoHhJrv0IuHKqjdJR3p5gckWp5TtUJM8nI42MomXj0SM2cyKMnitKv9-pny3pQrh459XP68bTkA5bjZKxtq3mkjbPbDfn028DKujj0KjjcLeH_s5JtXKD600PK8Kb7VbPJqyfnkbJkXhPJULx5bJeQm_q7PKJAhsbcNyU42bU47QbrH0xRfyNReQIO13hcdSRbkWJrpQT8r5-OIJUvP02DJ3bPEab3vOIOTXpO12M0zBN5thURB2DkO-4bCWJ5TMl5jDh3Mb6ksD-FtqtJHKbDDoCIKtfK; delPer=0; PSINO=7; ZFY=3nckonzJ6aE80MKyUr4y8zQoAPXHmiqyK35M1BfC5XY:C; BDCLND=tE3Nof7QnlSVboq1mb1gUQnAYUJV0sXIZ7bpW714pVE%3D; STOKEN=424372d096960b2d01ce2b9e63e4e05ac1302157657cc94cacdb6c903accb967; H_PS_PSSID=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; H_WISE_SIDS=62325_63145_63327_63881_63948_63275_64012_64016_64027_64058_64049_64056_64085_64091_64145_64072_64164; Hm_lvt_182d6d59474cf78db37e0b2248640ea5=1753414973; Hm_lpvt_182d6d59474cf78db37e0b2248640ea5=1753553612; ndut_fmt=C43E2B2038A6168778CCFA43292A4BD592ADBFBC98D2B62722F4AA2992B3EE66; ab_sr=1.0.1_NTI1ZDhhZGExN2U2ZjNjZGUyZDUwNjc3Yzg2YzQ0MjFkMGUxY2Q1MzFlYmJkYWFhN2E0ZjIzY2M1YmQwMDBiYzllNTZmNTI3MTRkNzU5NjYxYzY3NDAzY2UyMjRlNDJlZWM1YTBiNjIxZjgwNWJhNzE4MDBjNDRlYzc0MTY1NTQwM2M4MDhhY2VlN2E2NzEwZjNiNGFmZmViMzliMTVkMjg5MTZkMmRiZTA4NzJmZmU5NTNhZmE5YTViZTM2YWUy; PANPSC=7818778965945482978%3AnHZOtuqy9avxzjnZjz4PEFcS2d9ns3O5C61tf8CKQkjghmkhEDEibA3MPJHnDhkCz81ttRoL0tCAzpMjKfHvSlRJmYG59WfnDmIVxiIPZ%2BhtcQe%2BbgwEaTCyYQOwAMBA4CvFohGLzm4xCCiUPpEZ9rj2cUzyGc10baC532AuNi3w0iRW66wpccQp2WmIaeM4j9JYwYAV4WIE%2BW3bn8WQpiM3H8XB9kjAw%2FFO59HDGjBoPH46caSWouLaMusRzMQHj%2FrJb%2BRIEq3GB1Ggo%2FVEtCYpmymoXX%2BPeeyDNZbt4BHtb5iP4kyX6ZKxVlqrIDxM'
    const baiduService = new BaiduUploadService(baiduCookie)

    // 进度回调 - 优化重复日志显示
    let lastLoggedFile = ''
    let lastLoggedProgress = 0

    const onProgress = (progressData: TransferProgress) => {
      progress.value = progressData.totalProgress
      progressText.value = `正在${progressData.status === 'downloading' ? '下载' : '上传'}: ${progressData.currentFile}`

      // 只在文件切换或进度有显著变化时更新日志（避免重复显示）
      const currentProgress = Math.round(progressData.currentFileProgress)
      const progressDiff = Math.abs(currentProgress - lastLoggedProgress)

      if (progressData.currentFile !== lastLoggedFile) {
        // 新文件开始下载
        addLog(`正在下载: ${progressData.currentFile}`, 'info', { isSubTask: true })
        lastLoggedFile = progressData.currentFile
        lastLoggedProgress = 0
      } else if (progressDiff >= 10 && currentProgress > 0) {
        // 进度变化超过10%时才更新日志
        updateLastLog(`${progressData.currentFile}: ${currentProgress}% (${formatSpeed(progressData.speed)})`)
        lastLoggedProgress = currentProgress
      }
    }

    // 日志回调
    const onLog = (message: string, type: 'info' | 'success' | 'warning' | 'error') => {
      addLog(message, type, { showTime: type === 'error' || type === 'success' })
    }

    // 直接使用下载测试的逻辑 - 获取下载目录
    const { invoke } = await import('@tauri-apps/api/core')
    const appDir = await invoke('get_app_directory') as string
    const downloadDir = `${appDir}/downloads`

    // 直接使用下载测试的逻辑 - 创建下载目录
    await invoke('create_dir_all', { path: downloadDir })

    // 逐个处理文件
    let success = true
    for (const file of selectedFiles.value) {
      try {
        addLog(`开始下载: ${file.file_name}`, 'info')

        let downloadSuccess = false

        if (file.is_folder) {
          // 直接复制下载测试的文件夹下载代码
          let lastLoggedFile = ''
          downloadSuccess = await quarkService.downloadFolder(
            file.fid,
            downloadDir,
            (progressData) => {
              progress.value = (progressData.completed / progressData.total) * 50 // 下载占50%
              progressText.value = `下载文件夹: ${progressData.currentFile || file.file_name}`

              // 优化日志显示：只在新文件开始下载时记录，避免重复日志
              if (progressData.currentFile && progressData.currentFile !== lastLoggedFile) {
                addLog(`正在下载: ${progressData.currentFile}`, 'info')
                lastLoggedFile = progressData.currentFile
              }
            }
          )
        } else {
          // 直接复制下载测试的文件下载代码
          downloadSuccess = await quarkService.downloadFile(
            file.fid,
            downloadDir,
            (progressData) => {
              progress.value = progressData.percentage * 0.5 // 下载占50%
              progressText.value = `下载文件: ${file.file_name}`
            }
          )
        }

        if (downloadSuccess) {
          addLog(`下载成功: ${file.file_name}`, 'success')

          // 直接调用上传测试的代码 - 上传到百度网盘
          try {
            const localPath = `${downloadDir}/${file.file_name}`
            addLog(`开始上传到百度网盘: ${file.file_name}`, 'info')

            // 直接复制上传测试的逻辑 - 创建File对象
            const { invoke } = await import('@tauri-apps/api/core')
            const fileData = await invoke('read_file_bytes', { filePath: localPath }) as number[]
            const uint8Array = new Uint8Array(fileData)
            const fileObject = new File([uint8Array], file.file_name, {
              type: 'application/octet-stream'
            })

            // 直接复制上传测试的上传调用
            const uploadResult = await baiduService.uploadFile(
              fileObject,
              targetFolderPath.value,
              true, // 覆盖同名文件
              (uploadProgress) => {
                progress.value = 50 + (uploadProgress.percentage * 0.5) // 上传占50%
                progressText.value = `上传文件: ${file.file_name} (${uploadProgress.speedText})`
              }
            )

            if (uploadResult.success) {
              addLog(`上传成功: ${file.file_name}`, 'success')
            } else {
              addLog(`上传失败: ${file.file_name} - ${uploadResult.message}`, 'error')
              success = false
            }
          } catch (uploadError) {
            addLog(`上传失败: ${file.file_name} - ${uploadError}`, 'error')
            success = false
          }
        } else {
          addLog(`下载失败: ${file.file_name}`, 'error')
          success = false
        }
      } catch (error) {
        addLog(`处理文件失败: ${file.file_name} - ${error}`, 'error')
        success = false
      }
    }

    if (success) {
      progress.value = 100
      progressText.value = '传输完成'
      addLog('网盘间传输任务完成', 'success', { isTaskEnd: true, showTime: true })
      showMessage('传输完成！', 'success')
    } else {
      addLog('传输任务失败', 'error', { isTaskEnd: true, showTime: true })
      showMessage('传输失败，请查看日志了解详情', 'error')
    }

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    addLog(`传输失败: ${errorMsg}`, 'error', { showTime: true })
    showMessage('传输失败: ' + errorMsg, 'error')
  } finally {
    loading.value = false
  }
}

const resumeTask = () => {
  isPaused.value = false
  pauseController.value = new AbortController()
  addLog('传输已继续', 'info')
}

const pauseTask = () => {
  isPaused.value = true
  pauseController.value?.abort()
  addLog('传输已暂停', 'warning')
}

const togglePause = () => {
  if (isPaused.value) {
    resumeTask()
  } else {
    pauseTask()
  }
}

const stopTask = () => {
  loading.value = false
  isPaused.value = false
  pauseController.value?.abort()
  addLog('传输已停止', 'warning')
}

const clearLogs = () => {
  if (loading.value) {
    const choice = confirm('传输正在进行中，是否停止传输并清理日志？')
    if (choice) {
      stopTask()
      logs.value = []
    }
  } else {
    logs.value = []
  }
}

// 关闭文件浏览器
const closeFileBrowser = () => {
  showFileBrowser.value = false
}

// 处理文件选择
const handleFileSelected = (file: any) => {
  if (currentSelectionMode.value === 'source') {
    // 源文件选择模式
    if (file.dir) {
      showMessage('请选择文件，不是文件夹', 'warning')
      return
    }

    // 检查是否已经选择过这个文件
    const fileId = file.fid
    const existingIndex = selectedFiles.value.findIndex(f => f.fid === fileId)

    if (existingIndex >= 0) {
      showMessage('该文件已经选择过了', 'warning')
      return
    }

    // 添加到选择列表
    selectedFiles.value.push({
      fid: file.fid,
      file_name: file.file_name,
      size: file.size || 0,
      is_folder: false,
      showPreview: false
    })

    // 更新显示
    if (selectedFiles.value.length === 1) {
      sourceFilePath.value = selectedFiles.value[0].file_name
    } else {
      sourceFilePath.value = `已选择 ${selectedFiles.value.length} 个文件`
    }

    // 不关闭浏览器，允许继续选择
    showMessage(`已选择 ${selectedFiles.value.length} 个文件，可继续选择或关闭浏览器`, 'info')
  }
}

// 处理文件夹选择（仅用于源文件选择）
const handleFolderSelected = (folder: any) => {
  if (currentSelectionMode.value === 'source') {
    // 源文件选择模式 - 支持选择文件夹
    const existingIndex = selectedFiles.value.findIndex(f => f.fid === folder.fid)

    if (existingIndex >= 0) {
      showMessage('该文件夹已经选择过了', 'warning')
      return
    }

    // 添加文件夹到选择列表
    selectedFiles.value.push({
      fid: folder.fid,
      file_name: folder.file_name,
      size: folder.size || 0,
      is_folder: true,
      showPreview: false
    })

    // 更新显示
    if (selectedFiles.value.length === 1) {
      sourceFilePath.value = selectedFiles.value[0].file_name
    } else {
      sourceFilePath.value = `已选择 ${selectedFiles.value.length} 个项目`
    }

    showMessage(`已选择文件夹: ${folder.file_name}，可继续选择或关闭浏览器`, 'info')
  }
}

const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error', options: any = {}) => {
  logs.value.push({
    message,
    type,
    time: options.showTime ? new Date().toLocaleTimeString() : null,
    isTaskStart: options.isTaskStart || false,
    isTaskEnd: options.isTaskEnd || false,
    isSubTask: options.isSubTask || false,
    isSeparator: options.isSeparator || false,
    indent: options.indent || 0,
    ...options
  })
}

// 更新最后一条日志（避免重复添加）
const updateLastLog = (message: string) => {
  if (logs.value.length > 0) {
    const lastLog = logs.value[logs.value.length - 1]
    if (lastLog.isSubTask) {
      lastLog.message = message
    }
  }
}

// 工具函数
const getFileIcon = (file: any) => {
  if (file.is_folder) return 'fas fa-folder'
  const ext = file.file_name.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'mp4': case 'avi': case 'mkv': return 'fas fa-video'
    case 'mp3': case 'wav': case 'flac': return 'fas fa-music'
    case 'jpg': case 'png': case 'gif': return 'fas fa-image'
    case 'pdf': return 'fas fa-file-pdf'
    case 'doc': case 'docx': return 'fas fa-file-word'
    case 'zip': case 'rar': case '7z': return 'fas fa-file-archive'
    default: return 'fas fa-file'
  }
}

const getFileType = (filename: string) => {
  const ext = filename.split('.').pop()?.toLowerCase()
  return ext ? ext.toUpperCase() : '未知'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond: number) => {
  if (bytesPerSecond === 0) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



// 生命周期
onMounted(() => {
  // 检查登录状态
  if (!authStore.isLoggedIn) {
    showLoginPrompt.value = true
  }


})
</script>

<style scoped>
.cloud-transfer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* 消息提示 */
.message-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.message {
  padding: 0.75rem 1.25rem;
  border-radius: 0.375rem;
  color: white;
  font-weight: 500;
  font-size: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out;
}

.message-info {
  background: var(--primary-color);
}

.message-success {
  background: var(--success-color);
}

.message-warning {
  background: var(--warning-color);
}

.message-error {
  background: var(--error-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 左右两栏布局 - 4:6 比例 */
.transfer-layout {
  display: grid;
  grid-template-columns: 4fr 6fr;
  gap: 0.75rem;
  height: 100%;
  overflow: hidden;
}

/* 面板通用样式 */
.left-panel,
.right-panel {
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 左侧面板样式 - 紧凑布局 */
.left-panel {
  padding: 0;
}

.panel-title {
  padding: 0.6rem 0.8rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
  margin: 0;
  flex-shrink: 0;
}

/* 各个区域样式 */
.direction-section,
.source-section,
.target-section,
.action-section {
  padding: 0.6rem;
}

.direction-section {
  border-bottom: 1px solid var(--border-color);
}

.target-section {
  border-bottom: 1px solid var(--border-color);
}

.source-section {
  flex: 1;
  border-bottom: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.4rem;
}

.required {
  color: var(--error-color);
}

.direction-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.direction-option {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--bg-primary);
}

.direction-option:hover {
  border-color: var(--primary-color);
  background: var(--bg-tertiary);
}

.direction-option.active {
  border-color: var(--primary-color);
  background: var(--bg-tertiary);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.direction-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.source-disk, .target-disk {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.75rem;
}

.disk-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.arrow {
  color: var(--primary-color);
  font-size: 0.875rem;
}

/* 文件输入 - 紧凑布局 */
.file-input {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;
  gap: 0.4rem;
}

.file-input i {
  position: absolute;
  left: 0.5rem;
  color: var(--text-secondary);
  z-index: 1;
  font-size: 0.75rem;
}

/* 文件夹输入 - 紧凑布局 */
.folder-input {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;
  gap: 0.4rem;
}

.folder-input i {
  position: absolute;
  left: 0.5rem;
  color: var(--text-secondary);
  z-index: 1;
  font-size: 0.75rem;
}

.path-input {
  flex: 1;
  padding: 0.4rem 0.5rem 0.4rem 1.8rem;
  border: 1px solid var(--border-color);
  border-radius: 0.3rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.75rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.path-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 源文件输入框拉满宽度 */
.path-input-full {
  width: 100%;
}

.select-btn {
  padding: 0.4rem 0.6rem;
  border: 1px solid var(--primary-color);
  border-radius: 0.3rem;
  background: var(--primary-color);
  color: white;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
}

.select-btn:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.select-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.file-desc,
.folder-desc {
  font-size: 0.7rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

/* 已选择文件容器 - 拉满剩余空间 */
.selected-files-container {
  margin-top: 0.5rem;
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  overflow: hidden;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 已选择文件列表 */
.selected-files {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selected-files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.files-count {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.75rem;
}

.clear-all-btn {
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.3rem;
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.clear-all-btn:hover {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.selected-files-list {
  flex: 1;
  overflow-y: auto;
}

/* 空选择状态 */
.empty-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.empty-selection .empty-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
  text-align: center;
}

.empty-selection .empty-text i {
  font-size: 1.5rem;
  opacity: 0.5;
}

.selected-file-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.selected-file-item:last-child {
  border-bottom: none;
}

/* 文件卡片翻转效果 */
.file-card {
  flex: 1;
  height: 2rem;
  perspective: 1000px;
  cursor: pointer;
}

.file-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: left;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.file-card-inner.flipped {
  transform: rotateY(180deg);
}

.file-card-front, .file-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
}

.file-card-back {
  transform: rotateY(180deg);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  width: 100%;
}

.file-icon {
  font-size: 0.875rem;
  color: var(--primary-color);
  width: 1rem;
  text-align: center;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.7rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.file-meta {
  display: flex;
  gap: 0.375rem;
  color: var(--text-secondary);
  font-size: 0.65rem;
  margin-top: 0.125rem;
}

.file-preview-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.65rem;
}

.preview-item .label {
  color: var(--text-secondary);
}

.preview-item .value {
  color: var(--text-primary);
  font-weight: 500;
}

.remove-btn {
  padding: 0.125rem 0.25rem;
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: 0.2rem;
  cursor: pointer;
  font-size: 0.6rem;
  margin-left: 0.375rem;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: var(--error-hover);
}





/* 执行按钮 */
.execute-btn {
  width: 100%;
  padding: 0.6rem 1rem;
  border: 1px solid var(--primary-color);
  border-radius: 0.375rem;
  background: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
}

.execute-btn.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.execute-btn.btn-success {
  background: var(--success-color);
  border-color: var(--success-color);
}

.execute-btn.btn-success:hover:not(:disabled) {
  background: var(--success-hover);
  border-color: var(--success-hover);
}

.execute-btn.btn-disabled {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* 右侧面板样式 */
.right-panel {
  display: flex;
  flex-direction: column;
  perspective: 1000px;
}

/* 翻转容器 */
.flip-container {
  flex: 1;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s ease-in-out;
}

.flip-container.flipped {
  transform: rotateY(180deg);
}

/* 翻转面 */
.flip-front,
.flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 0.75rem;
  overflow: hidden;
}

.flip-front {
  z-index: 2;
}

.flip-back {
  transform: rotateY(180deg);
  z-index: 1;
}

.results-panel,
.browser-panel {
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
  border-radius: 0.75rem;
  height: 100%;
  min-height: 0;
}

/* 浏览器头部 */
.browser-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.close-browser-btn {
  padding: 0.3rem 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.3rem;
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.close-browser-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.browser-container {
  flex: 1;
  overflow: hidden;
}

/* 日志面板 */
.log-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.log-title {
  padding: 0.6rem 0.8rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex-shrink: 0;
}

/* 标题控制图标 */
.log-controls {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  border: none;
  background: transparent;
}

.control-btn:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.control-btn.btn-warning {
  color: var(--warning-color);
}

.control-btn.btn-warning:hover {
  color: var(--warning-color);
  background: rgba(251, 146, 60, 0.1);
}

.control-btn.btn-success {
  color: var(--success-color);
}

.control-btn.btn-success:hover {
  color: var(--success-color);
  background: rgba(34, 197, 94, 0.1);
}

.control-btn.btn-danger {
  color: var(--error-color);
}

.control-btn.btn-danger:hover {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  transform: scale(1.1);
}

.control-btn.btn-secondary:hover {
  color: var(--error-color);
}

/* 进度条 */
.progress-section {
  margin: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-tertiary);
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-text {
  color: var(--text-primary);
  font-size: 0.75rem;
}

.progress-percentage {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.75rem;
}

.progress-bar {
  height: 0.375rem;
  background: var(--border-color);
  border-radius: 0.1875rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

/* 日志容器 */
.log-container {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
  margin: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

/* 空日志状态 */
.empty-logs {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 0.75rem;
  text-align: center;
}

/* 日志列表 */
.log-list {
  flex: 1;
}

.log-item {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.75rem;
  line-height: 1.4;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.info {
  color: var(--text-primary);
}

.log-item.success {
  color: var(--success-color);
}

.log-item.warning {
  color: var(--warning-color);
}

.log-item.error {
  color: var(--error-color);
}

.log-item.separator {
  border-top: 1px solid var(--border-color);
  margin-top: 0.5rem;
  padding-top: 0.5rem;
}

.log-item.task-start {
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-bottom: 0.25rem;
}

.log-item.task-end {
  font-weight: 600;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

.log-item.sub-task {
  font-size: 0.7rem;
  opacity: 0.8;
}

.log-time {
  color: var(--text-secondary);
  font-size: 0.7rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
}

.log-message.no-time {
  margin-left: 0;
}
</style>
