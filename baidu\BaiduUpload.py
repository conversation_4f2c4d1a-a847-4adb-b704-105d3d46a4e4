import requests
import json
import time
import hashlib
import os

class BaiduUpload:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Origin': 'https://pan.baidu.com',
            'Referer': 'https://pan.baidu.com/disk/main?_at_=1694654780804',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Pragma': 'no-cache',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        self.bdstoken = None
        
    def login_with_cookie(self, cookie_str):
        """使用cookie登录"""
        self.headers['Cookie'] = cookie_str
        
    def get_bdstoken(self):
        """获取bdstoken"""
        if self.bdstoken:
            return self.bdstoken
            
        url = 'https://pan.baidu.com/api/gettemplatevariable'
        params = {
            'clienttype': 0,
            'app_id': '250528',
            'web': 1,
            'fields': '["bdstoken","token"]'
        }
        try:
            resp = self.session.get(url, params=params, headers=self.headers)
            result = resp.json()
            if result.get('errno') == 0:
                self.bdstoken = result['result']['bdstoken']
                return self.bdstoken
            else:
                print(f'获取bdstoken失败 - 错误码: {result.get("errno")}')
                return None
        except Exception as e:
            print(f'获取bdstoken出错: {str(e)}')
            return None

    def calculate_md5(self, file_path):
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def calculate_chunk_md5(self, chunk_data):
        """计算分片数据的MD5"""
        return hashlib.md5(chunk_data).hexdigest()

    def calculate_file_block_list(self, file_path, chunk_size=4*1024*1024):
        """计算文件的分片MD5列表（用于precreate）"""
        block_list = []
        file_size = os.path.getsize(file_path)

        print(f"🔄 正在计算文件分片MD5...")
        with open(file_path, 'rb') as f:
            offset = 0
            part_seq = 0

            while offset < file_size:
                # 读取分片数据
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break

                # 计算分片MD5
                chunk_md5 = self.calculate_chunk_md5(chunk_data)
                block_list.append(chunk_md5)

                offset += len(chunk_data)
                part_seq += 1

                if part_seq % 50 == 0:
                    print(f"   已处理: {part_seq} 个分片")

        print(f"✅ 分片MD5计算完成，共 {len(block_list)} 个分片")
        return block_list

    def get_part_list(self, uploadid, file_path, part_ids):
        """获取服务器要求的分片信息"""
        print(f"🔄 获取分片信息: {part_ids[:10]}{'...' if len(part_ids) > 10 else ''} (共{len(part_ids)}个)")

        url = 'http://pan.baidu.com/api/batch/getpartlist'

        # 使用移动端参数（与抓包一致）
        params = {
            'app_id': '250528',
            'vip': '2',
            'version': '12.11.0',
            'queryfree': '1',
            'channel': 'iPhone_17.1.1_iPhone12ProMax_chunlei_1099a_wifi',
            'idfa': '00000000-0000-0000-0000-000000000000',
            'apn_id': '1_0',
            'rand': f'{int(time.time() * 1000000):x}',
            'network_type': 'wifi',
            'freeisp': '0',
            'devuid': 'd58639d41c05a744823cb1ff4236d40c8ec1cde9',
            'cuid': '78123A6CBF44126F3348123A3B93661349935AA25OLPQMRARFF',
            'activestatus': '0',
            'time': str(int(time.time())),
            'clienttype': '1',
            'rand2': f'{int(time.time() * 1000000):x}',
            'idfv': '57E7804F-4974-4597-A4A2-2227D0B9DC74',
            'logid': f'MjAyNTA3MzAxNTA4MDczNjAsZDU4NjM5ZDQxYzA1YTc0NDgyM2NiMWZmNDIzNmQ0MGM4ZWMxY2RlOSw5MzM5',
            'bgstatus': '1'
        }

        # 构建分片ID列表（与抓包格式一致）
        part_list_str = json.dumps(part_ids)

        data = {
            'uploadid': uploadid,
            'path': file_path,
            'part_list': part_list_str
        }

        # 设置headers（与抓包一致）
        headers = self.headers.copy()
        headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8'
        headers['Connection'] = 'close'
        headers['User-Agent'] = 'netdisk;12.11.0;iPhone12ProMax;ios-iphone;17.1.1;zh_CN'
        headers['X-Download-From'] = 'baiduyun'

        try:
            resp = self.session.post(url, params=params, data=data, headers=headers)
            result = resp.json()

            if result.get('errno') == 0:
                part_list = result.get('part_list', [])
                print(f"✅ 获取到 {len(part_list)} 个分片信息")
                if part_list:
                    for part in part_list[:3]:  # 只显示前3个
                        print(f"   分片 {part['part_id']}: {part['part_size']/(1024*1024):.1f}MB, MD5: {part['part_md5'][:8]}...")
                    if len(part_list) > 3:
                        print(f"   ... 还有 {len(part_list) - 3} 个分片")
                return part_list
            else:
                print(f"❌ 获取分片信息失败 - 错误码: {result.get('errno')}")
                print(f"   错误信息: {result}")
                return None

        except Exception as e:
            print(f"❌ 获取分片信息出错: {str(e)}")
            return None

    def read_file_chunk_by_server_info(self, file_path, part_info):
        """根据服务器返回的分片信息读取文件数据"""
        part_id = part_info['part_id']
        part_size = part_info['part_size']
        expected_md5 = part_info['part_md5']

        # 计算偏移量（假设按4MB分片）
        offset = part_id * (4 * 1024 * 1024)

        with open(file_path, 'rb') as f:
            f.seek(offset)
            chunk_data = f.read(part_size)

            # 验证MD5
            actual_md5 = self.calculate_chunk_md5(chunk_data)
            if actual_md5 != expected_md5:
                print(f"⚠️  分片 {part_id} MD5不匹配: 期望 {expected_md5}, 实际 {actual_md5}")

            return {
                'part_id': part_id,
                'offset': offset,
                'size': len(chunk_data),
                'md5': actual_md5,
                'data': chunk_data
            }

    def precreate_upload_large_file(self, file_path, target_path="/", use_mobile_api=True):
        """预创建大文件上传任务（按正确流程）"""
        print(f"🔄 步骤1: 预创建大文件上传任务...")

        bdstoken = self.get_bdstoken()
        if not bdstoken:
            return None

        # 计算文件信息
        file_size = os.path.getsize(file_path)
        filename = os.path.basename(file_path)
        file_mtime = int(os.path.getmtime(file_path))

        # 构建上传路径
        upload_path = target_path.rstrip('/') + '/' + filename
        if upload_path.startswith('//'):
            upload_path = upload_path[1:]

        # 计算所有分片的MD5列表（用于precreate）
        block_list = self.calculate_file_block_list(file_path)
        block_list_str = json.dumps(block_list)

        # 选择API版本
        if use_mobile_api:
            url = 'https://pan.baidu.com/api/precreate'
            params = {
                'app_id': '250528',
                'vip': '2',
                'version': '12.11.0',
                'queryfree': '1',
                'channel': 'iPhone_17.1.1_iPhone12ProMax_chunlei_1099a_wifi',
                'clienttype': '1',  # 移动端
                'time': str(int(time.time())),
                'bdstoken': bdstoken,
                'dp-logid': str(int(time.time() * 1000000)) + '0001'
            }
        else:
            url = 'https://pan.baidu.com/api/precreate'
            params = {
                'bdstoken': bdstoken,
                'app_id': '250528',
                'channel': 'chunlei',
                'web': '1',
                'clienttype': '0',  # 网页端
                'dp-logid': str(int(time.time() * 1000000)) + '0001'
            }

        data = {
            'path': upload_path,
            'autoinit': '1',
            'block_list': block_list_str,
            'target_path': target_path,
            'local_mtime': str(file_mtime),
            'size': str(file_size),
            'isdir': '0'
        }

        self.headers['Content-Type'] = 'application/x-www-form-urlencoded'

        try:
            resp = self.session.post(url, params=params, data=data, headers=self.headers)
            result = resp.json()

            if result.get('errno') == 0:
                uploadid = result['uploadid']
                server_block_list = result.get('block_list', [])

                print(f"✅ 预创建成功")
                print(f"   - 文件路径: {upload_path}")
                print(f"   - 文件大小: {file_size} bytes ({file_size/(1024**3):.2f} GB)")
                print(f"   - 预计分片数: {len(block_list)}")
                print(f"   - 服务器要求上传: {len(server_block_list)} 个分片")
                print(f"   - UploadID: {uploadid}")

                return {
                    'uploadid': uploadid,
                    'path': upload_path,
                    'size': file_size,
                    'mtime': file_mtime,
                    'target_path': target_path,
                    'client_block_list': block_list,
                    'server_block_list': server_block_list,
                    'return_type': result.get('return_type', 1)
                }
            else:
                print(f"❌ 预创建失败 - 错误码: {result.get('errno')}")
                print(f"   错误信息: {result}")
                return None

        except Exception as e:
            print(f"❌ 预创建出错: {str(e)}")
            return None

    def precreate_upload(self, file_path, target_path="/"):
        """第一步：预创建上传任务（兼容原有小文件上传）"""
        file_size = os.path.getsize(file_path)

        # 如果文件大于100MB，使用大文件上传模式
        if file_size > 100 * 1024 * 1024:
            print(f"📁 检测到大文件 ({file_size/(1024**3):.2f} GB)，使用分片上传模式")
            return self.precreate_upload_large_file(file_path, target_path, use_mobile_api=True)

        # 原有的小文件上传逻辑
        print(f"🔄 步骤1: 预创建上传任务...")

        bdstoken = self.get_bdstoken()
        if not bdstoken:
            return None

        # 计算文件信息
        file_md5 = self.calculate_md5(file_path)
        filename = os.path.basename(file_path)
        file_mtime = int(os.path.getmtime(file_path))

        # 构建上传路径
        upload_path = target_path.rstrip('/') + '/' + filename
        if upload_path.startswith('//'):
            upload_path = upload_path[1:]

        url = 'https://pan.baidu.com/api/precreate'
        params = {
            'bdstoken': bdstoken,
            'app_id': '250528',
            'channel': 'chunlei',
            'web': '1',
            'clienttype': '0',
            'dp-logid': str(int(time.time() * 1000000)) + '0001'
        }

        data = {
            'path': upload_path,
            'autoinit': '1',
            'block_list': f'["{file_md5}"]',
            'target_path': target_path,
            'local_mtime': str(file_mtime)
        }

        self.headers['Content-Type'] = 'application/x-www-form-urlencoded'

        try:
            resp = self.session.post(url, params=params, data=data, headers=self.headers)
            result = resp.json()

            if result.get('errno') == 0:
                print(f"✅ 预创建成功")
                print(f"   - 文件路径: {upload_path}")
                print(f"   - 文件大小: {file_size} bytes")
                print(f"   - 文件MD5: {file_md5}")
                print(f"   - UploadID: {result['uploadid']}")

                return {
                    'uploadid': result['uploadid'],
                    'path': upload_path,
                    'size': file_size,
                    'md5': file_md5,
                    'mtime': file_mtime,
                    'target_path': target_path,
                    'block_list': result.get('block_list', [0])
                }
            else:
                print(f"❌ 预创建失败 - 错误码: {result.get('errno')}")
                print(f"   错误信息: {result}")
                return None

        except Exception as e:
            print(f"❌ 预创建出错: {str(e)}")
            return None

    def upload_chunk_by_server_info(self, file_path, part_info, upload_info, use_mobile_api=True):
        """根据服务器返回的分片信息上传分片"""
        part_id = part_info['part_id']
        part_size = part_info['part_size']
        expected_md5 = part_info['part_md5']

        print(f"🔄 上传分片 {part_id} ({part_size/(1024*1024):.1f}MB)")

        # 根据服务器信息读取文件数据
        chunk_info = self.read_file_chunk_by_server_info(file_path, part_info)
        chunk_data = chunk_info['data']

        url = 'https://bdbl-cm01.pcs.baidu.com/rest/2.0/pcs/superfile2'

        if use_mobile_api:
            params = {
                'method': 'upload',
                'type': 'tmpfile',
                'app_id': '250528',
                'vip': '2',
                'version': '12.11.0',
                'clienttype': '1',  # 移动端
                'filename': os.path.basename(upload_info['path']),
                'path': '/',
                'uploadid': upload_info['uploadid'],
                'partseq': str(part_id),
                'partoffset': str(chunk_info['offset']),
                'x-part-offset': str(part_size),
                'x-part-etag': expected_md5,
                'x-enable-resume': '1',
                'time': str(int(time.time())),
                'dp-logid': str(int(time.time() * 1000000)) + f'{part_id:04d}'
            }
        else:
            params = {
                'method': 'upload',
                'logid': 'MTc1MzMzNjE1OTMxNjAuMDUwMTU5NTY3NzcxNjMwMjI=',
                'app_id': '250528',
                'channel': 'chunlei',
                'web': '1',
                'clienttype': '0',
                'path': upload_info['path'],
                'uploadid': upload_info['uploadid'],
                'uploadsign': '0',
                'partseq': str(part_id),
                'dp-logid': str(int(time.time() * 1000000)) + f'{part_id:04d}'
            }

        # 准备multipart/form-data
        boundary = f'0xKhTmLbOuNdArY-{int(time.time() * 1000000)}'

        # 构建multipart数据
        multipart_data = (
            f'--{boundary}\r\n'
            f'Content-Disposition: form-data; name="file"; filename="file"\r\n'
            f'Content-Type: application/octet-stream\r\n'
            f'\r\n'
        ).encode('utf-8')

        multipart_data += chunk_data
        multipart_data += f'\r\n--{boundary}--\r\n'.encode('utf-8')

        # 设置headers
        upload_headers = self.headers.copy()
        upload_headers['Content-Type'] = f'multipart/form-data; charset=utf-8; boundary={boundary}'
        upload_headers['Content-Length'] = str(len(multipart_data))
        upload_headers['Connection'] = 'close'

        if use_mobile_api:
            upload_headers['User-Agent'] = 'netdisk;12.11.0;iPhone12ProMax;ios-iphone;17.1.1;zh_CN'
            upload_headers['X-Download-From'] = 'baiduyun'
        else:
            upload_headers['Sec-Fetch-Site'] = 'same-site'
            upload_headers['Referer'] = 'https://pan.baidu.com/'

        try:
            resp = self.session.post(url, params=params, data=multipart_data, headers=upload_headers)
            result = resp.json()

            if result.get('md5'):
                print(f"   ✅ 分片 {part_id} 上传成功 (MD5: {result['md5'][:8]}...)")
                return result
            else:
                print(f"   ❌ 分片 {part_id} 上传失败: {result}")
                return None

        except Exception as e:
            print(f"   ❌ 分片 {part_id} 上传出错: {str(e)}")
            return None

    def upload_file_content(self, file_path, upload_info):
        """第二步：上传文件内容（按正确流程）"""
        # 检查是否为大文件分片上传
        if 'server_block_list' in upload_info:
            return self.upload_chunks_by_server_info(file_path, upload_info)

        # 原有的单文件上传逻辑
        print(f"🔄 步骤2: 上传文件内容...")

        url = 'https://bdbl-cm01.pcs.baidu.com/rest/2.0/pcs/superfile2'
        params = {
            'method': 'upload',
            'logid': 'MTc1MzMzNjE1OTMxNjAuMDUwMTU5NTY3NzcxNjMwMjI=',
            'app_id': '250528',
            'channel': 'chunlei',
            'web': '1',
            'clienttype': '0',
            'path': upload_info['path'],
            'uploadid': upload_info['uploadid'],
            'uploadsign': '0',
            'partseq': '0',
            'dp-logid': str(int(time.time() * 1000000)) + '0002'
        }

        # 准备multipart/form-data
        boundary = '----WebKitFormBoundaryRGge7B0LHvi1n8iB'

        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()

        # 构建multipart数据
        multipart_data = (
            f'--{boundary}\r\n'
            f'Content-Disposition: form-data; name="file"; filename="blob"\r\n'
            f'Content-Type: application/octet-stream\r\n'
            f'\r\n'
        ).encode('utf-8')

        multipart_data += file_content
        multipart_data += f'\r\n--{boundary}--\r\n'.encode('utf-8')

        # 设置headers
        upload_headers = self.headers.copy()
        upload_headers['Content-Type'] = f'multipart/form-data; boundary={boundary}'
        upload_headers['Sec-Fetch-Site'] = 'same-site'
        upload_headers['Referer'] = 'https://pan.baidu.com/'

        try:
            resp = self.session.post(url, params=params, data=multipart_data, headers=upload_headers)
            result = resp.json()

            if result.get('md5'):
                print(f"✅ 文件上传成功")
                print(f"   - 返回MD5: {result['md5']}")
                print(f"   - 分片序号: {result['partseq']}")
                return result
            else:
                print(f"❌ 文件上传失败: {result}")
                return None

        except Exception as e:
            print(f"❌ 文件上传出错: {str(e)}")
            return None

    def upload_chunks_by_server_info(self, file_path, upload_info):
        """按服务器要求的分片信息上传"""
        print(f"🔄 步骤2: 开始分片上传...")

        uploadid = upload_info['uploadid']
        server_block_list = upload_info['server_block_list']

        if not server_block_list:
            print("✅ 服务器表示无需上传分片（文件已存在）")
            return {'md5': 'server_skip'}

        print(f"📊 服务器要求上传 {len(server_block_list)} 个分片")

        # 步骤2.1: 分批获取分片信息（避免请求过大）
        batch_size = 50  # 每批查询50个分片
        all_part_list = []

        for i in range(0, len(server_block_list), batch_size):
            batch_ids = server_block_list[i:i + batch_size]
            print(f"🔄 获取分片信息批次 {i//batch_size + 1}/{(len(server_block_list) + batch_size - 1)//batch_size} ({len(batch_ids)} 个分片)")

            part_list = self.get_part_list(uploadid, upload_info['path'], batch_ids)
            if part_list:
                all_part_list.extend(part_list)
            else:
                print(f"❌ 批次 {i//batch_size + 1} 获取分片信息失败")
                return None

            # 添加延迟避免频率限制
            if i + batch_size < len(server_block_list):
                time.sleep(0.5)

        if not all_part_list:
            print("❌ 未获取到任何分片信息")
            return None

        print(f"✅ 总共获取到 {len(all_part_list)} 个分片信息")

        # 步骤2.2: 按服务器要求上传分片
        uploaded_chunks = []
        failed_chunks = []

        for i, part_info in enumerate(all_part_list):
            try:
                print(f"📤 上传进度: {i + 1}/{len(all_part_list)} ({((i + 1)/len(all_part_list)*100):.1f}%)")
                result = self.upload_chunk_by_server_info(file_path, part_info, upload_info, use_mobile_api=True)
                if result:
                    uploaded_chunks.append(result)
                else:
                    failed_chunks.append(part_info['part_id'])

                # 添加延迟避免频率限制
                if i < len(all_part_list) - 1:
                    time.sleep(0.1)

            except Exception as e:
                print(f"   ❌ 分片 {part_info['part_id']} 上传异常: {str(e)}")
                failed_chunks.append(part_info['part_id'])

        if failed_chunks:
            print(f"❌ 有 {len(failed_chunks)} 个分片上传失败: {failed_chunks}")
            return None
        else:
            print(f"✅ 所有 {len(all_part_list)} 个分片上传成功")
            # 返回最后一个分片的结果，用于后续处理
            return uploaded_chunks[-1] if uploaded_chunks else {'md5': 'all_uploaded'}

    def create_file_record(self, upload_info, upload_result):
        """第三步：创建文件记录（按正确流程）"""
        print(f"🔄 步骤3: 创建文件记录...")

        bdstoken = self.get_bdstoken()
        if not bdstoken:
            return None

        url = 'https://pan.baidu.com/api/create'

        # 检查是否为分片上传
        if 'client_block_list' in upload_info:
            # 分片上传模式 - 使用客户端计算的完整MD5列表
            params = {
                'isdir': '0',
                'bdstoken': bdstoken,
                'app_id': '250528',
                'vip': '2',
                'version': '12.11.0',
                'clienttype': '1',  # 移动端
                'time': str(int(time.time())),
                'dp-logid': str(int(time.time() * 1000000)) + '0003'
            }

            # 使用客户端计算的完整分片MD5列表
            block_list_str = json.dumps(upload_info['client_block_list'])

            data = {
                'path': upload_info['path'],
                'size': str(upload_info['size']),
                'uploadid': upload_info['uploadid'],
                'block_list': block_list_str,
                'target_path': upload_info['target_path'],
                'local_mtime': str(upload_info['mtime']),
                'isdir': '0'
            }
        else:
            # 单文件上传模式
            params = {
                'isdir': '0',
                'bdstoken': bdstoken,
                'app_id': '250528',
                'channel': 'chunlei',
                'web': '1',
                'clienttype': '0',
                'dp-logid': str(int(time.time() * 1000000)) + '0003'
            }

            data = {
                'path': upload_info['path'],
                'size': str(upload_info['size']),
                'uploadid': upload_info['uploadid'],
                'block_list': f'["{upload_result["md5"]}"]',
                'target_path': upload_info['target_path'],
                'local_mtime': str(upload_info['mtime'])
            }

        self.headers['Content-Type'] = 'application/x-www-form-urlencoded'

        try:
            resp = self.session.post(url, params=params, data=data, headers=self.headers)
            result = resp.json()

            if result.get('errno') == 0:
                print(f"✅ 文件创建成功")
                print(f"   - 文件ID: {result['fs_id']}")
                print(f"   - 服务器文件名: {result['server_filename']}")
                print(f"   - 文件路径: {result['path']}")
                print(f"   - 文件大小: {result['size']} bytes")
                if 'client_block_list' in upload_info:
                    print(f"   - 分片数量: {len(upload_info['client_block_list'])}")
                return result
            else:
                print(f"❌ 文件创建失败 - 错误码: {result.get('errno')}")
                print(f"   错误信息: {result}")
                return None

        except Exception as e:
            print(f"❌ 文件创建出错: {str(e)}")
            return None

    def get_quota_info(self):
        """第四步：获取配额信息"""
        print(f"🔄 步骤4: 获取存储配额信息...")
        
        url = 'https://pan.baidu.com/api/quota'
        params = {
            'clienttype': '0',
            'app_id': '250528',
            'web': '1',
            'dp-logid': str(int(time.time() * 1000000)) + '0074'
        }
        
        quota_headers = self.headers.copy()
        quota_headers['Accept'] = 'application/json, text/plain, */*'
        quota_headers['X-Requested-With'] = 'XMLHttpRequest'
        
        try:
            resp = self.session.get(url, params=params, headers=quota_headers)
            result = resp.json()
            
            if result.get('errno') == 0:
                total_gb = result['total'] / (1024**3)
                used_gb = result['used'] / (1024**3)
                free_gb = total_gb - used_gb
                
                print(f"✅ 存储配额信息")
                print(f"   - 总容量: {total_gb:.2f} GB")
                print(f"   - 已使用: {used_gb:.2f} GB")
                print(f"   - 剩余容量: {free_gb:.2f} GB")
                return result
            else:
                print(f"❌ 获取配额失败 - 错误码: {result.get('errno')}")
                return None
                
        except Exception as e:
            print(f"❌ 获取配额出错: {str(e)}")
            return None

    def refresh_file_list(self, dir_path="/"):
        """第五步：刷新文件列表"""
        print(f"🔄 步骤5: 刷新文件列表...")
        
        url = 'https://pan.baidu.com/api/list'
        params = {
            'clienttype': '0',
            'app_id': '250528',
            'web': '1',
            'dp-logid': str(int(time.time() * 1000000)) + '0075',
            'order': 'name',
            'desc': '1',
            'dir': dir_path,
            'num': '100',
            'page': '1'
        }
        
        list_headers = self.headers.copy()
        list_headers['Accept'] = 'application/json, text/plain, */*'
        list_headers['X-Requested-With'] = 'XMLHttpRequest'
        
        try:
            resp = self.session.get(url, params=params, headers=list_headers)
            result = resp.json()
            
            if result.get('errno') == 0:
                file_count = len(result.get('list', []))
                print(f"✅ 文件列表刷新成功")
                print(f"   - 目录: {dir_path}")
                print(f"   - 文件数量: {file_count}")
                return result
            else:
                print(f"❌ 刷新文件列表失败 - 错误码: {result.get('errno')}")
                return None
                
        except Exception as e:
            print(f"❌ 刷新文件列表出错: {str(e)}")
            return None

    def upload_file_core(self, file_path, target_path="/"):
        """核心文件上传流程（只包含前3个API）"""
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None

        try:
            # 步骤1: 预创建
            upload_info = self.precreate_upload(file_path, target_path)
            if not upload_info:
                return None

            time.sleep(0.3)  # 短暂延迟

            # 步骤2: 上传内容
            upload_result = self.upload_file_content(file_path, upload_info)
            if not upload_result:
                return None

            time.sleep(0.3)  # 短暂延迟

            # 步骤3: 创建记录
            create_result = self.create_file_record(upload_info, upload_result)
            if not create_result:
                return None

            return create_result

        except Exception as e:
            print(f"❌ 上传过程出错: {str(e)}")
            return None

    def upload_file(self, file_path, target_path="/", skip_final_steps=False):
        """完整的文件上传流程"""
        print(f"🚀 开始上传文件: {os.path.basename(file_path)}")
        print(f"📁 目标路径: {target_path}")
        if not skip_final_steps:
            print("=" * 50)

        start_time = time.time()

        # 执行核心上传流程
        create_result = self.upload_file_core(file_path, target_path)
        if not create_result:
            return False

        # 如果不跳过最终步骤，执行配额和列表刷新
        if not skip_final_steps:
            time.sleep(0.5)  # 短暂延迟

            # 步骤4: 获取配额
            self.get_quota_info()

            time.sleep(0.5)  # 短暂延迟

            # 步骤5: 刷新列表
            self.refresh_file_list(target_path)

            end_time = time.time()
            duration = end_time - start_time

            print("=" * 50)
            print(f"🎉 文件上传完成!")
            print(f"⏱️  总耗时: {duration:.2f} 秒")
            print(f"📄 文件名: {create_result['server_filename']}")
            print(f"📍 文件路径: {create_result['path']}")
            print(f"📊 文件大小: {create_result['size']} bytes")
        else:
            print(f"✅ 文件上传成功: {create_result['server_filename']}")

        return True

    def batch_upload_files(self, file_list, target_path="/", delay_between_files=1):
        """批量上传文件（优化版，只在最后请求配额和列表）"""
        if not file_list:
            print("❌ 文件列表为空")
            return False

        print(f"🚀 开始批量上传 {len(file_list)} 个文件")
        print(f"📁 目标路径: {target_path}")
        print("=" * 60)

        start_time = time.time()
        success_count = 0
        failed_files = []

        # 批量上传文件（只执行前3个API）
        for i, file_path in enumerate(file_list, 1):
            print(f"\n📤 上传进度: {i}/{len(file_list)} - {os.path.basename(file_path)}")

            try:
                # 使用核心上传流程，跳过最终步骤
                success = self.upload_file(file_path, target_path, skip_final_steps=True)

                if success:
                    success_count += 1
                else:
                    failed_files.append(file_path)

                # 添加延迟避免频率限制
                if i < len(file_list):  # 最后一个文件不需要延迟
                    time.sleep(delay_between_files)

            except Exception as e:
                print(f"❌ 上传文件出错: {str(e)}")
                failed_files.append(file_path)

        # 所有文件上传完成后，执行最终步骤
        print(f"\n🔄 批量上传完成，正在获取最终信息...")

        try:
            # 获取配额信息
            self.get_quota_info()
            time.sleep(0.5)

            # 刷新文件列表
            self.refresh_file_list(target_path)

        except Exception as e:
            print(f"⚠️  获取最终信息失败: {str(e)}")

        # 输出批量上传结果
        end_time = time.time()
        duration = end_time - start_time

        print("=" * 60)
        print(f"🎉 批量上传完成!")
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"✅ 成功上传: {success_count}/{len(file_list)} 个文件")

        if failed_files:
            print(f"❌ 失败文件: {len(failed_files)} 个")
            for failed_file in failed_files[:5]:  # 只显示前5个失败文件
                print(f"   - {os.path.basename(failed_file)}")
            if len(failed_files) > 5:
                print(f"   ... 还有 {len(failed_files) - 5} 个失败文件")

        return success_count > 0

    def upload_folder_structure(self, local_folder_path, target_path="/"):
        """上传文件夹结构（保持完整的文件夹层级）"""
        if not os.path.exists(local_folder_path):
            print(f"❌ 本地文件夹不存在: {local_folder_path}")
            return False

        if not os.path.isdir(local_folder_path):
            print(f"❌ 路径不是文件夹: {local_folder_path}")
            return False

        print(f"🔍 扫描文件夹: {local_folder_path}")

        # 收集所有文件及其相对路径
        file_mappings = []
        for root, _, files in os.walk(local_folder_path):
            for filename in files:
                local_file_path = os.path.join(root, filename)
                # 计算相对路径
                relative_path = os.path.relpath(local_file_path, local_folder_path)
                # 构建网盘中的完整路径
                folder_name = os.path.basename(local_folder_path)
                remote_path = target_path.rstrip('/') + '/' + folder_name + '/' + relative_path.replace('\\', '/')
                # 确保路径格式正确
                if remote_path.startswith('//'):
                    remote_path = remote_path[1:]

                file_mappings.append({
                    'local_path': local_file_path,
                    'remote_path': remote_path,
                    'relative_path': relative_path,
                    'size': os.path.getsize(local_file_path)
                })

        if not file_mappings:
            print("❌ 文件夹中没有文件")
            return False

        print(f"📊 发现 {len(file_mappings)} 个文件")

        # 显示文件列表（前10个）
        print(f"\n📄 文件映射列表:")
        for i, mapping in enumerate(file_mappings[:10]):
            print(f"   {i+1:2d}. {mapping['relative_path']} -> {mapping['remote_path']} ({mapping['size']} bytes)")

        if len(file_mappings) > 10:
            print(f"   ... 还有 {len(file_mappings) - 10} 个文件")

        # 询问用户确认
        print(f"\n✅ 新发现: 百度网盘支持完整文件夹路径!")
        print(f"   📁 将保持完整的文件夹层级结构")
        print(f"   🎯 文件夹名: {os.path.basename(local_folder_path)}")
        print(f"   📍 目标位置: {target_path}")

        user_input = input(f"\n是否继续上传这 {len(file_mappings)} 个文件（保持文件夹结构）？(y/N): ").strip().lower()
        if user_input != 'y':
            print("❌ 用户取消上传")
            return False

        # 执行文件夹结构上传
        return self.batch_upload_folder_files(file_mappings, delay_between_files=0.8)

    def batch_upload_folder_files(self, file_mappings, delay_between_files=1):
        """批量上传文件夹中的文件（保持文件夹结构）"""
        if not file_mappings:
            print("❌ 文件映射列表为空")
            return False

        print(f"🚀 开始批量上传 {len(file_mappings)} 个文件（保持文件夹结构）")
        print("=" * 60)

        start_time = time.time()
        success_count = 0
        failed_files = []

        # 批量上传文件（保持文件夹路径）
        for i, mapping in enumerate(file_mappings, 1):
            print(f"\n📤 上传进度: {i}/{len(file_mappings)} - {mapping['relative_path']}")
            print(f"   📍 目标路径: {mapping['remote_path']}")

            try:
                # 使用完整的远程路径上传文件
                success = self.upload_file_with_path(mapping['local_path'], mapping['remote_path'])

                if success:
                    success_count += 1
                else:
                    failed_files.append(mapping)

                # 添加延迟避免频率限制
                if i < len(file_mappings):  # 最后一个文件不需要延迟
                    time.sleep(delay_between_files)

            except Exception as e:
                print(f"❌ 上传文件出错: {str(e)}")
                failed_files.append(mapping)

        # 所有文件上传完成后，执行最终步骤
        print(f"\n🔄 批量上传完成，正在获取最终信息...")

        try:
            # 获取配额信息
            self.get_quota_info()
            time.sleep(0.5)

            # 刷新文件列表
            self.refresh_file_list("/")

        except Exception as e:
            print(f"⚠️  获取最终信息失败: {str(e)}")

        # 输出批量上传结果
        end_time = time.time()
        duration = end_time - start_time

        print("=" * 60)
        print(f"🎉 文件夹上传完成!")
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"✅ 成功上传: {success_count}/{len(file_mappings)} 个文件")

        if failed_files:
            print(f"❌ 失败文件: {len(failed_files)} 个")
            for failed_mapping in failed_files[:5]:  # 只显示前5个失败文件
                print(f"   - {failed_mapping['relative_path']}")
            if len(failed_files) > 5:
                print(f"   ... 还有 {len(failed_files) - 5} 个失败文件")

        return success_count > 0

    def upload_file_with_path(self, local_file_path, remote_file_path):
        """上传文件到指定的远程路径（包含文件夹结构）"""
        print(f"🚀 开始上传文件: {os.path.basename(local_file_path)}")
        print(f"📁 远程路径: {remote_file_path}")

        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            print(f"❌ 文件不存在: {local_file_path}")
            return False

        try:
            # 步骤1: 预创建（使用完整远程路径）
            upload_info = self.precreate_upload_with_path(local_file_path, remote_file_path)
            if not upload_info:
                return False

            time.sleep(0.3)  # 短暂延迟

            # 步骤2: 上传内容
            upload_result = self.upload_file_content(local_file_path, upload_info)
            if not upload_result:
                return False

            time.sleep(0.3)  # 短暂延迟

            # 步骤3: 创建记录
            create_result = self.create_file_record(upload_info, upload_result)
            if not create_result:
                return False

            print(f"✅ 文件上传成功: {create_result['server_filename']}")
            return True

        except Exception as e:
            print(f"❌ 上传过程出错: {str(e)}")
            return False

    def precreate_upload_with_path(self, file_path, remote_path):
        """预创建上传任务（支持完整远程路径）"""
        print(f"🔄 步骤1: 预创建上传任务...")

        bdstoken = self.get_bdstoken()
        if not bdstoken:
            return None

        # 计算文件信息
        file_size = os.path.getsize(file_path)
        file_md5 = self.calculate_md5(file_path)
        file_mtime = int(os.path.getmtime(file_path))

        url = 'https://pan.baidu.com/api/precreate'
        params = {
            'bdstoken': bdstoken,
            'app_id': '250528',
            'channel': 'chunlei',
            'web': '1',
            'clienttype': '0',
            'dp-logid': str(int(time.time() * 1000000)) + '0001'
        }

        data = {
            'path': remote_path,  # 使用完整的远程路径
            'autoinit': '1',
            'block_list': f'["{file_md5}"]',
            'target_path': '/',  # 目标路径保持为根目录
            'local_mtime': str(file_mtime)
        }

        self.headers['Content-Type'] = 'application/x-www-form-urlencoded'

        try:
            resp = self.session.post(url, params=params, data=data, headers=self.headers)
            result = resp.json()

            if result.get('errno') == 0:
                print(f"✅ 预创建成功")
                print(f"   - 远程路径: {remote_path}")
                print(f"   - 文件大小: {file_size} bytes")
                print(f"   - 文件MD5: {file_md5}")
                print(f"   - UploadID: {result['uploadid']}")

                return {
                    'uploadid': result['uploadid'],
                    'path': remote_path,
                    'size': file_size,
                    'md5': file_md5,
                    'mtime': file_mtime,
                    'target_path': '/',
                    'block_list': result.get('block_list', [0])
                }
            else:
                print(f"❌ 预创建失败 - 错误码: {result.get('errno')}")
                print(f"   错误信息: {result}")
                return None

        except Exception as e:
            print(f"❌ 预创建出错: {str(e)}")
            return None

    def upload_large_file(self, file_path, target_path="/", chunk_size=4*1024*1024):
        """专门用于大文件上传的方法"""
        file_size = os.path.getsize(file_path)
        print(f"🚀 开始大文件上传: {os.path.basename(file_path)}")
        print(f"📁 目标路径: {target_path}")
        print(f"📊 文件大小: {file_size/(1024**3):.2f} GB")
        print(f"🔧 分片大小: {chunk_size/(1024*1024):.0f} MB")
        print("=" * 60)

        start_time = time.time()

        try:
            # 步骤1: 预创建上传任务
            upload_info = self.precreate_upload_large_file(file_path, target_path, use_mobile_api=True)
            if not upload_info:
                return False

            time.sleep(0.5)

            # 步骤2: 上传所有分片
            upload_result = self.upload_file_content(file_path, upload_info)
            if not upload_result:
                return False

            time.sleep(0.5)

            # 步骤3: 创建文件记录
            create_result = self.create_file_record(upload_info, upload_result)
            if not create_result:
                return False

            # 步骤4: 获取配额信息
            time.sleep(0.5)
            self.get_quota_info()

            # 步骤5: 刷新文件列表
            time.sleep(0.5)
            self.refresh_file_list(target_path)

            end_time = time.time()
            duration = end_time - start_time

            print("=" * 60)
            print(f"🎉 大文件上传完成!")
            print(f"⏱️  总耗时: {duration:.2f} 秒")
            print(f"📄 文件名: {create_result['server_filename']}")
            print(f"📍 文件路径: {create_result['path']}")
            print(f"📊 文件大小: {create_result['size']} bytes ({int(create_result['size'])/(1024**3):.2f} GB)")
            print(f"🔧 分片数量: {len(upload_info['chunks'])}")
            print(f"⚡ 平均速度: {file_size/(1024*1024)/duration:.2f} MB/s")

            return True

        except Exception as e:
            print(f"❌ 大文件上传过程出错: {str(e)}")
            return False


def main():
    """测试上传功能"""
    uploader = BaiduUpload()

    # 从cookies.json读取cookie
    try:
        with open('cookies.json', 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            baidu_cookie = cookies_data.get('baidu', '')

        if not baidu_cookie:
            print("❌ 未找到百度网盘Cookie，请先配置cookies.json")
            return

        uploader.login_with_cookie(baidu_cookie)

        # 询问用户选择测试类型
        print("请选择测试类型:")
        print("1. 小文件上传测试 (创建测试文件)")
        print("2. 大文件上传测试 (需要指定现有文件)")
        print("3. 自动检测文件大小并选择合适的上传方式")

        choice = input("请输入选择 (1/2/3): ").strip()

        if choice == "1":
            # 小文件测试
            test_file = "test_upload.txt"

            # 创建测试文件
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("这是一个测试上传的文件\n")
                f.write(f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("百度网盘上传功能测试\n")

            # 执行上传
            success = uploader.upload_file(test_file, "/")

            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)

        elif choice == "2":
            # 大文件测试
            file_path = input("请输入大文件路径: ").strip().strip('"')
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return

            file_size = os.path.getsize(file_path)
            print(f"文件大小: {file_size/(1024**3):.2f} GB")

            confirm = input("确认上传此文件吗? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消上传")
                return

            success = uploader.upload_large_file(file_path, "/")

        elif choice == "3":
            # 自动检测
            file_path = input("请输入文件路径: ").strip().strip('"')
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return

            success = uploader.upload_file(file_path, "/")

        else:
            print("❌ 无效选择")
            return

        if success:
            print("\n✅ 上传测试成功!")
        else:
            print("\n❌ 上传测试失败!")

    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")


if __name__ == "__main__":
    main()
