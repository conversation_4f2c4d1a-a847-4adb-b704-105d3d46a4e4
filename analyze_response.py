#!/usr/bin/env python3
import gzip
import json

# 读取响应体
with open('20250730_160209/20250730_160209/0/response_body', 'rb') as f:
    data = f.read()

print(f"数据长度: {len(data)}")
print(f"完整hex: {data.hex()}")

# 尝试不同的方法解析
print("\n=== 尝试1: 直接解析JSON ===")
try:
    result = json.loads(data.decode('utf-8'))
    print("成功解析JSON:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
except Exception as e:
    print(f"直接解析失败: {e}")

print("\n=== 尝试2: 跳过前面的长度信息 ===")
# HTTP chunked encoding 可能有长度信息
lines = data.split(b'\r\n')
for i, line in enumerate(lines):
    print(f"行 {i}: {line[:50]}...")
    if len(line) > 10:
        try:
            # 尝试解析这一行
            result = json.loads(line.decode('utf-8'))
            print(f"在行 {i} 找到JSON:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            break
        except:
            continue

print("\n=== 尝试3: 查找JSON开始位置 ===")
# 查找 { 字符
for i, byte in enumerate(data):
    if byte == ord('{'):
        print(f"找到JSON开始位置: {i}")
        json_data = data[i:]
        try:
            # 找到JSON结束位置
            brace_count = 0
            end_pos = -1
            for j, b in enumerate(json_data):
                if b == ord('{'):
                    brace_count += 1
                elif b == ord('}'):
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = j + 1
                        break
            
            if end_pos > 0:
                json_str = json_data[:end_pos].decode('utf-8')
                result = json.loads(json_str)
                print("成功解析JSON:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                break
        except Exception as e:
            print(f"解析失败: {e}")
            continue
