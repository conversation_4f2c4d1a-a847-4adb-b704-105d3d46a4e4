#!/usr/bin/env python3
import gzip
import json

# 读取压缩的响应体
with open('20250730_160209/20250730_160209/0/response_body', 'rb') as f:
    data = f.read()

print(f"原始数据长度: {len(data)}")
print(f"前20字节: {data[:20].hex()}")

# 找到gzip头 (1f 8b)
gzip_start = -1
for i in range(len(data) - 1):
    if data[i] == 0x1f and data[i+1] == 0x8b:
        gzip_start = i
        break

if gzip_start >= 0:
    print(f"找到gzip头在位置: {gzip_start}")
    gzip_data = data[gzip_start:]
    
    try:
        decompressed = gzip.decompress(gzip_data)
        print(f"解压后长度: {len(decompressed)}")
        
        # 尝试解析JSON
        result = json.loads(decompressed.decode('utf-8'))
        print("解压成功！JSON内容:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"解压失败: {e}")
        print(f"gzip数据前20字节: {gzip_data[:20].hex()}")
else:
    print("未找到gzip头")
